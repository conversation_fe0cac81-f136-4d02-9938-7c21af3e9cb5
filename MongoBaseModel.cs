﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TestVoicePen
{
    /// <summary>
    /// 数据基类
    /// </summary>
    public abstract class MongoBaseModel
    {
        /// <summary>
        /// mongodb主键id
        /// </summary>
        [BsonId]
        [BsonRepresentation(BsonType.Int64)]
        public long Mid { get; set; }

        /// <summary>
        /// 入库时间
        /// </summary>
        [BsonRepresentation(BsonType.DateTime)]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime AddTime { get; set; } = DateTime.Now;
    }
}
