C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\TestVoicePen.exe
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\TestVoicePen.deps.json
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\TestVoicePen.runtimeconfig.json
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\TestVoicePen.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\TestVoicePen.pdb
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\Azure.Core.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\Azure.Identity.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\Dapper.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\Microsoft.Identity.Client.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\Microsoft.Identity.Client.Extensions.Msal.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\Microsoft.IdentityModel.Abstractions.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\Microsoft.IdentityModel.Protocols.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\Microsoft.SqlServer.Server.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\Microsoft.Win32.SystemEvents.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\Newtonsoft.Json.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\SkiaSharp.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\System.Configuration.ConfigurationManager.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\System.Drawing.Common.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\System.Memory.Data.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\System.Runtime.Caching.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\System.Security.Permissions.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\System.Windows.Extensions.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\runtimes\unix\lib\net6.0\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\runtimes\win\lib\net6.0\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\runtimes\win-arm\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\runtimes\win-arm64\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\runtimes\win-x64\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\runtimes\win-x86\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\runtimes\win\lib\net6.0\Microsoft.Win32.SystemEvents.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\runtimes\osx\native\libSkiaSharp.dylib
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\runtimes\win-arm64\native\libSkiaSharp.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\runtimes\win-x64\native\libSkiaSharp.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\runtimes\win-x86\native\libSkiaSharp.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\runtimes\unix\lib\net6.0\System.Drawing.Common.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\runtimes\win\lib\net6.0\System.Drawing.Common.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\runtimes\win\lib\net6.0\System.Runtime.Caching.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\runtimes\win\lib\net6.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\bin\Debug\net6.0\runtimes\win\lib\net6.0\System.Windows.Extensions.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\obj\Debug\net6.0\TestVoicePen.csproj.AssemblyReference.cache
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\obj\Debug\net6.0\TestVoicePen.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\obj\Debug\net6.0\TestVoicePen.AssemblyInfoInputs.cache
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\obj\Debug\net6.0\TestVoicePen.AssemblyInfo.cs
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\obj\Debug\net6.0\TestVoicePen.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\obj\Debug\net6.0\TestVoicePen.csproj.CopyComplete
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\obj\Debug\net6.0\TestVoicePen.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\obj\Debug\net6.0\refint\TestVoicePen.dll
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\obj\Debug\net6.0\TestVoicePen.pdb
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\obj\Debug\net6.0\TestVoicePen.genruntimeconfig.cache
C:\Users\<USER>\Desktop\TestCanvas\TestVoicePen\obj\Debug\net6.0\ref\TestVoicePen.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\TestVoicePen.exe
D:\Van\TestVoicePen\bin\Debug\net6.0\TestVoicePen.deps.json
D:\Van\TestVoicePen\bin\Debug\net6.0\TestVoicePen.runtimeconfig.json
D:\Van\TestVoicePen\bin\Debug\net6.0\TestVoicePen.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\TestVoicePen.pdb
D:\Van\TestVoicePen\bin\Debug\net6.0\Azure.Core.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\Azure.Identity.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\Dapper.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\DnsClient.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\Microsoft.Bcl.AsyncInterfaces.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\Microsoft.Data.SqlClient.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\Microsoft.Extensions.Logging.Abstractions.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\Microsoft.Identity.Client.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\Microsoft.Identity.Client.Extensions.Msal.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\Microsoft.IdentityModel.Abstractions.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\Microsoft.IdentityModel.JsonWebTokens.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\Microsoft.IdentityModel.Logging.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\Microsoft.IdentityModel.Protocols.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\Microsoft.IdentityModel.Tokens.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\Microsoft.SqlServer.Server.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\Microsoft.Win32.SystemEvents.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\MongoDB.Bson.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\MongoDB.Driver.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\Newtonsoft.Json.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\SharpCompress.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\SkiaSharp.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\Snappier.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\System.Configuration.ConfigurationManager.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\System.Drawing.Common.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\System.IdentityModel.Tokens.Jwt.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\System.Memory.Data.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\System.Runtime.Caching.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\System.Security.Cryptography.ProtectedData.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\System.Security.Permissions.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\System.Windows.Extensions.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\ZstdSharp.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\runtimes\unix\lib\net6.0\Microsoft.Data.SqlClient.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\runtimes\win\lib\net6.0\Microsoft.Data.SqlClient.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\runtimes\win-arm\native\Microsoft.Data.SqlClient.SNI.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\runtimes\win-arm64\native\Microsoft.Data.SqlClient.SNI.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\runtimes\win-x64\native\Microsoft.Data.SqlClient.SNI.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\runtimes\win-x86\native\Microsoft.Data.SqlClient.SNI.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\runtimes\win\lib\net6.0\Microsoft.Win32.SystemEvents.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\runtimes\osx\native\libSkiaSharp.dylib
D:\Van\TestVoicePen\bin\Debug\net6.0\runtimes\win-arm64\native\libSkiaSharp.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\runtimes\win-x64\native\libSkiaSharp.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\runtimes\win-x86\native\libSkiaSharp.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\runtimes\unix\lib\net6.0\System.Drawing.Common.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\runtimes\win\lib\net6.0\System.Drawing.Common.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\runtimes\win\lib\net6.0\System.Runtime.Caching.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\runtimes\win\lib\net6.0\System.Security.Cryptography.ProtectedData.dll
D:\Van\TestVoicePen\bin\Debug\net6.0\runtimes\win\lib\net6.0\System.Windows.Extensions.dll
D:\Van\TestVoicePen\obj\Debug\net6.0\TestVoicePen.csproj.AssemblyReference.cache
D:\Van\TestVoicePen\obj\Debug\net6.0\TestVoicePen.GeneratedMSBuildEditorConfig.editorconfig
D:\Van\TestVoicePen\obj\Debug\net6.0\TestVoicePen.AssemblyInfoInputs.cache
D:\Van\TestVoicePen\obj\Debug\net6.0\TestVoicePen.AssemblyInfo.cs
D:\Van\TestVoicePen\obj\Debug\net6.0\TestVoicePen.csproj.CoreCompileInputs.cache
D:\Van\TestVoicePen\obj\Debug\net6.0\TestVoic.2F2EF2C1.Up2Date
D:\Van\TestVoicePen\obj\Debug\net6.0\TestVoicePen.dll
D:\Van\TestVoicePen\obj\Debug\net6.0\refint\TestVoicePen.dll
D:\Van\TestVoicePen\obj\Debug\net6.0\TestVoicePen.pdb
D:\Van\TestVoicePen\obj\Debug\net6.0\TestVoicePen.genruntimeconfig.cache
D:\Van\TestVoicePen\obj\Debug\net6.0\ref\TestVoicePen.dll
