using MongoDB.Bson;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace TestVoicePen
{
    /// <summary>
    /// MongoDB点阵笔日志数据访问实现
    /// 将MongoDB的嵌套文档结构转换为扁平化的点位数据
    /// 支持按Mac地址和日期过滤数据
    /// </summary>
    public class MongoPenLogRepository : IPenLogRepository
    {
        private readonly IMongoCollection<PenLog> _collection;
        private readonly string _macAddress;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="connectionString">MongoDB连接字符串</param>
        /// <param name="databaseName">数据库名称</param>
        /// <param name="collectionName">集合名称</param>
        /// <param name="macAddress">要查询的Mac地址，如果为空则查询所有Mac</param>
        public MongoPenLogRepository(string connectionString, string databaseName, string collectionName, string macAddress = null)
        {
            var client = new MongoClient(connectionString);
            var database = client.GetDatabase(databaseName);
            _collection = database.GetCollection<PenLog>(collectionName);
            _macAddress = macAddress;
        }

        /// <summary>
        /// 获取所有页面列表（去重并排序）
        /// 根据Mac地址和当天日期过滤数据
        /// </summary>
        /// <returns>页面ID列表</returns>
        public async Task<IEnumerable<WorkbookPage>> GetPagesAsync()
        {
            // 构建查询条件
            var matchConditions = new List<BsonDocument>();

            // 添加当天日期过滤条件
            var today = DateTime.Today;
            var tomorrow = today.AddDays(1);
            matchConditions.Add(new BsonDocument("AddTime", new BsonDocument
            {
                { "$gte", today },
                { "$lt", tomorrow }
            }));

            // 如果指定了Mac地址，添加Mac过滤条件
            if (!string.IsNullOrEmpty(_macAddress))
            {
                matchConditions.Add(new BsonDocument("Mac", _macAddress));
            }

            // 构建聚合管道
            var pipeline = new List<BsonDocument>();

            // 添加匹配条件
            if (matchConditions.Any())
            {
                pipeline.Add(new BsonDocument("$match", new BsonDocument("$and", new BsonArray(matchConditions))));
            }

            // 分组获取不重复的页面ID
            pipeline.Add(new BsonDocument("$group", new BsonDocument
            {
                { "_id", "$Page" }
            }));

            // 排序
            pipeline.Add(new BsonDocument("$sort", new BsonDocument("_id", 1)));

            // 投影
            pipeline.Add(new BsonDocument("$project", new BsonDocument
            {
                { "_id", 0 },
                { "PageId", "$_id" }
            }));

            var result = await _collection.Aggregate<WorkbookPage>(pipeline).ToListAsync();
            return result;
        }

        /// <summary>
        /// 根据页面ID获取该页面的所有点位数据
        /// 将MongoDB的嵌套Dots结构转换为扁平化的点位列表
        /// 根据Mac地址和当天日期过滤数据
        /// </summary>
        /// <param name="pageId">页面ID</param>
        /// <returns>点位数据列表</returns>
        public async Task<IEnumerable<PenLogPoint>> GetPointsByPageAsync(int pageId)
        {
            // 构建查询条件
            var filterBuilder = Builders<PenLog>.Filter;
            var filters = new List<FilterDefinition<PenLog>>
            {
                filterBuilder.Eq(x => x.Page, pageId)
            };

            // 添加当天日期过滤条件
            var today = DateTime.Today;
            var tomorrow = today.AddDays(1);
            filters.Add(filterBuilder.Gte(x => x.AddTime, today));
            filters.Add(filterBuilder.Lt(x => x.AddTime, tomorrow));

            // 如果指定了Mac地址，添加Mac过滤条件
            if (!string.IsNullOrEmpty(_macAddress))
            {
                filters.Add(filterBuilder.Eq(x => x.Mac, _macAddress));
            }

            // 组合所有过滤条件
            var combinedFilter = filterBuilder.And(filters);
            var penLogs = await _collection.Find(combinedFilter).ToListAsync();

            var points = new List<PenLogPoint>();
            
            // 将每个PenLog文档的Dots列表转换为扁平化的点位数据
            foreach (var penLog in penLogs)
            {
                if (penLog.Dots != null && penLog.Dots.Any())
                {
                    foreach (var dot in penLog.Dots)
                    {
                        points.Add(new PenLogPoint
                        {
                            Oid = dot.Mid, // 使用MongoDB的Mid作为Oid
                            X = dot.X,
                            Y = dot.Y,
                            PMac = penLog.Mac ?? string.Empty, // 使用PenLog的Mac地址
                            Page = penLog.Page,
                            Type = dot.Type,
                            UserId = penLog.UserId ?? string.Empty,
                            Pressure = dot.Pressure,
                            BookNo = dot.BookNo
                        });
                    }
                }
            }

            // 按照Mid排序，保持数据的时序性
            return points.OrderBy(p => p.Oid);
        }

        /// <summary>
        /// 清空所有点阵笔日志数据
        /// 根据Mac地址和当天日期过滤数据
        /// </summary>
        /// <returns></returns>
        public async Task ClearAllLogsAsync()
        {
            // 构建删除条件
            var filterBuilder = Builders<PenLog>.Filter;
            var filters = new List<FilterDefinition<PenLog>>();

            // 添加当天日期过滤条件
            var today = DateTime.Today;
            var tomorrow = today.AddDays(1);
            filters.Add(filterBuilder.Gte(x => x.AddTime, today));
            filters.Add(filterBuilder.Lt(x => x.AddTime, tomorrow));

            // 如果指定了Mac地址，添加Mac过滤条件
            if (!string.IsNullOrEmpty(_macAddress))
            {
                filters.Add(filterBuilder.Eq(x => x.Mac, _macAddress));
            }

            // 组合所有过滤条件
            var combinedFilter = filters.Any() ? filterBuilder.And(filters) : filterBuilder.Empty;

            // 删除符合条件的文档
            var result = await _collection.DeleteManyAsync(combinedFilter);
            Console.WriteLine($"MongoDB: 删除了 {result.DeletedCount} 条记录");
        }
    }
}
