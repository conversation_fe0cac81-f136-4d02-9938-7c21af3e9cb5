using MongoDB.Bson;
using MongoDB.Driver;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace TestVoicePen
{
    /// <summary>
    /// MongoDB点阵笔日志数据访问实现
    /// 将MongoDB的嵌套文档结构转换为扁平化的点位数据
    /// </summary>
    public class MongoPenLogRepository : IPenLogRepository
    {
        private readonly IMongoCollection<PenLog> _collection;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="connectionString">MongoDB连接字符串</param>
        /// <param name="databaseName">数据库名称</param>
        /// <param name="collectionName">集合名称</param>
        public MongoPenLogRepository(string connectionString, string databaseName, string collectionName)
        {
            var client = new MongoClient(connectionString);
            var database = client.GetDatabase(databaseName);
            _collection = database.GetCollection<PenLog>(collectionName);
        }

        /// <summary>
        /// 获取所有页面列表（去重并排序）
        /// </summary>
        /// <returns>页面ID列表</returns>
        public async Task<IEnumerable<WorkbookPage>> GetPagesAsync()
        {
            // 使用聚合管道获取不重复的页面ID并排序
            var pipeline = new[]
            {
                new BsonDocument("$group", new BsonDocument
                {
                    { "_id", "$Page" }
                }),
                new BsonDocument("$sort", new BsonDocument("_id", 1)),
                new BsonDocument("$project", new BsonDocument
                {
                    { "_id", 0 },
                    { "PageId", "$_id" }
                })
            };

            var result = await _collection.Aggregate<WorkbookPage>(pipeline).ToListAsync();
            return result;
        }

        /// <summary>
        /// 根据页面ID获取该页面的所有点位数据
        /// 将MongoDB的嵌套Dots结构转换为扁平化的点位列表
        /// </summary>
        /// <param name="pageId">页面ID</param>
        /// <returns>点位数据列表</returns>
        public async Task<IEnumerable<PenLogPoint>> GetPointsByPageAsync(int pageId)
        {
            // 查询指定页面的所有PenLog文档
            var filter = Builders<PenLog>.Filter.Eq(x => x.Page, pageId);
            var penLogs = await _collection.Find(filter).ToListAsync();

            var points = new List<PenLogPoint>();
            
            // 将每个PenLog文档的Dots列表转换为扁平化的点位数据
            foreach (var penLog in penLogs)
            {
                if (penLog.Dots != null && penLog.Dots.Any())
                {
                    foreach (var dot in penLog.Dots)
                    {
                        points.Add(new PenLogPoint
                        {
                            Oid = dot.Mid, // 使用MongoDB的Mid作为Oid
                            X = dot.X,
                            Y = dot.Y,
                            PMac = penLog.Mac ?? string.Empty, // 使用PenLog的Mac地址
                            Page = penLog.Page,
                            Type = dot.Type,
                            UserId = penLog.UserId ?? string.Empty,
                            Pressure = dot.Pressure,
                            BookNo = dot.BookNo
                        });
                    }
                }
            }

            // 按照Mid排序，保持数据的时序性
            return points.OrderBy(p => p.Oid);
        }

        /// <summary>
        /// 清空所有点阵笔日志数据
        /// </summary>
        /// <returns></returns>
        public async Task ClearAllLogsAsync()
        {
            // 删除集合中的所有文档
            await _collection.DeleteManyAsync(Builders<PenLog>.Filter.Empty);
        }
    }
}
