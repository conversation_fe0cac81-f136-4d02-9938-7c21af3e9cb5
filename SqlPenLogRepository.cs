using Dapper;
using Microsoft.Data.SqlClient;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace TestVoicePen
{
    /// <summary>
    /// SQL Server点阵笔日志数据访问实现
    /// 保持原有的SQL查询逻辑
    /// 支持按Mac地址和日期过滤数据
    /// </summary>
    public class SqlPenLogRepository : IPenLogRepository
    {
        private readonly string _connectionString;
        private readonly string _macAddress;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="connectionString">SQL Server连接字符串</param>
        /// <param name="macAddress">要查询的Mac地址，如果为空则查询所有Mac</param>
        public SqlPenLogRepository(string connectionString, string macAddress = null)
        {
            _connectionString = connectionString;
            _macAddress = macAddress;
        }

        /// <summary>
        /// 获取所有页面ID列表（去重并排序）
        /// 根据Mac地址和当天日期过滤数据
        /// </summary>
        /// <returns>页面ID列表</returns>
        public async Task<IEnumerable<int>> GetPagesAsync()
        {
            var whereConditions = new List<string> { "CAST([CreateTime] AS DATE) = CAST(GETDATE() AS DATE)" };
            var parameters = new DynamicParameters();

            // 如果指定了Mac地址，添加Mac过滤条件
            if (!string.IsNullOrEmpty(_macAddress))
            {
                whereConditions.Add("[PMac] = @macAddress");
                parameters.Add("macAddress", _macAddress);
            }

            var whereClause = string.Join(" AND ", whereConditions);
            var sql = $@"SELECT [Page]
                        FROM [dbo].[ZPenLog]
                        WHERE {whereClause}
                        GROUP BY [Page]
                        ORDER BY [Page];";

            await using var connection = new SqlConnection(_connectionString);
            return await connection.QueryAsync<int>(sql, parameters);
        }

        /// <summary>
        /// 根据页面ID获取该页面的所有点位数据
        /// 根据Mac地址和当天日期过滤数据
        /// </summary>
        /// <param name="pageId">页面ID</param>
        /// <returns>点位数据列表</returns>
        public async Task<IEnumerable<PenLogPoint>> GetPointsByPageAsync(int pageId)
        {
            var whereConditions = new List<string>
            {
                "[Page] = @page",
                "CAST([CreateTime] AS DATE) = CAST(GETDATE() AS DATE)"
            };
            var parameters = new DynamicParameters();
            parameters.Add("page", pageId);

            // 如果指定了Mac地址，添加Mac过滤条件
            if (!string.IsNullOrEmpty(_macAddress))
            {
                whereConditions.Add("[PMac] = @macAddress");
                parameters.Add("macAddress", _macAddress);
            }

            var whereClause = string.Join(" AND ", whereConditions);
            var sql = $@"SELECT [Oid],
                               [X],
                               [Y],
                               [PMac],
                               [Page],
                               [Type]
                        FROM [TestDotPen].[dbo].[ZPenLog]
                        WHERE {whereClause}
                        ORDER BY [Oid];";

            await using var connection = new SqlConnection(_connectionString);
            var sqlPoints = await connection.QueryAsync<SqlPenLogPoint>(sql, parameters);

            // 转换为统一的PenLogPoint格式
            var points = new List<PenLogPoint>();
            foreach (var sqlPoint in sqlPoints)
            {
                points.Add(new PenLogPoint
                {
                    Oid = sqlPoint.Oid,
                    X = sqlPoint.X,
                    Y = sqlPoint.Y,
                    PMac = sqlPoint.PMac ?? string.Empty,
                    Page = sqlPoint.Page,
                    Type = sqlPoint.Type,
                    UserId = string.Empty, // SQL版本没有UserId
                    Pressure = 0, // SQL版本没有Pressure
                    BookNo = 0 // SQL版本没有BookNo
                });
            }

            return points;
        }

        /// <summary>
        /// 清空所有点阵笔日志数据
        /// 根据Mac地址和当天日期过滤数据
        /// </summary>
        /// <returns></returns>
        public async Task ClearAllLogsAsync()
        {
            var whereConditions = new List<string> { "CAST([CreateTime] AS DATE) = CAST(GETDATE() AS DATE)" };
            var parameters = new DynamicParameters();

            // 如果指定了Mac地址，添加Mac过滤条件
            if (!string.IsNullOrEmpty(_macAddress))
            {
                whereConditions.Add("[PMac] = @macAddress");
                parameters.Add("macAddress", _macAddress);
            }

            var whereClause = string.Join(" AND ", whereConditions);
            var sql = $@"DELETE FROM [dbo].[ZPenLog] WHERE {whereClause};";

            await using var connection = new SqlConnection(_connectionString);
            var affectedRows = await connection.ExecuteAsync(sql, parameters);
            Console.WriteLine($"SQL Server: 删除了 {affectedRows} 条记录");
        }
    }

    /// <summary>
    /// SQL Server查询结果的原始数据模型
    /// 对应ZPenLog表的结构
    /// </summary>
    internal class SqlPenLogPoint
    {
        public long Oid { get; set; }
        public int X { get; set; }
        public int Y { get; set; }
        public string PMac { get; set; }
        public int Page { get; set; }
        public int Type { get; set; }
    }
}
