using Dapper;
using Microsoft.Data.SqlClient;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace TestVoicePen
{
    /// <summary>
    /// SQL Server点阵笔日志数据访问实现
    /// 保持原有的SQL查询逻辑
    /// </summary>
    public class SqlPenLogRepository : IPenLogRepository
    {
        private readonly string _connectionString;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="connectionString">SQL Server连接字符串</param>
        public SqlPenLogRepository(string connectionString)
        {
            _connectionString = connectionString;
        }

        /// <summary>
        /// 获取所有页面列表（去重并排序）
        /// </summary>
        /// <returns>页面ID列表</returns>
        public async Task<IEnumerable<WorkbookPage>> GetPagesAsync()
        {
            const string sql = @"SELECT [Page] AS [PageId]
                                FROM [dbo].[ZPenLog]
                                GROUP BY [Page]
                                ORDER BY [Page];";

            await using var connection = new SqlConnection(_connectionString);
            return await connection.QueryAsync<WorkbookPage>(sql);
        }

        /// <summary>
        /// 根据页面ID获取该页面的所有点位数据
        /// </summary>
        /// <param name="pageId">页面ID</param>
        /// <returns>点位数据列表</returns>
        public async Task<IEnumerable<PenLogPoint>> GetPointsByPageAsync(int pageId)
        {
            const string sql = @"SELECT [Oid],
                                       [X],
                                       [Y],
                                       [PMac],
                                       [Page],
                                       [Type]
                                FROM [TestDotPen].[dbo].[ZPenLog]
                                WHERE [Page] = @page
                                ORDER BY [Oid];";

            await using var connection = new SqlConnection(_connectionString);
            var sqlPoints = await connection.QueryAsync<SqlPenLogPoint>(sql, new { page = pageId });

            // 转换为统一的PenLogPoint格式
            var points = new List<PenLogPoint>();
            foreach (var sqlPoint in sqlPoints)
            {
                points.Add(new PenLogPoint
                {
                    Oid = sqlPoint.Oid,
                    X = sqlPoint.X,
                    Y = sqlPoint.Y,
                    PMac = sqlPoint.PMac ?? string.Empty,
                    Page = sqlPoint.Page,
                    Type = sqlPoint.Type,
                    UserId = string.Empty, // SQL版本没有UserId
                    Pressure = 0, // SQL版本没有Pressure
                    BookNo = 0 // SQL版本没有BookNo
                });
            }

            return points;
        }

        /// <summary>
        /// 清空所有点阵笔日志数据
        /// </summary>
        /// <returns></returns>
        public async Task ClearAllLogsAsync()
        {
            const string sql = @"TRUNCATE TABLE [dbo].[ZPenLog];";
            
            await using var connection = new SqlConnection(_connectionString);
            await connection.ExecuteAsync(sql);
        }
    }

    /// <summary>
    /// SQL Server查询结果的原始数据模型
    /// 对应ZPenLog表的结构
    /// </summary>
    internal class SqlPenLogPoint
    {
        public long Oid { get; set; }
        public int X { get; set; }
        public int Y { get; set; }
        public string PMac { get; set; }
        public int Page { get; set; }
        public int Type { get; set; }
    }
}
