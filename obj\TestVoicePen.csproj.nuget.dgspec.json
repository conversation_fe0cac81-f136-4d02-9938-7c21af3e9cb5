{"format": 1, "restore": {"D:\\Van\\TestVoicePen\\TestVoicePen.csproj": {}}, "projects": {"D:\\Van\\TestVoicePen\\TestVoicePen.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Van\\TestVoicePen\\TestVoicePen.csproj", "projectName": "TestVoicePen", "projectPath": "D:\\Van\\TestVoicePen\\TestVoicePen.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Van\\TestVoicePen\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://www.myget.org/F/stackoverflow/api/v3/index.json": {}, "https://www.nuget.org/api/v2/": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Dapper": {"target": "Package", "version": "[2.0.123, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.1.1, )"}, "MongoDB.Driver": {"target": "Package", "version": "[3.4.2, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "SkiaSharp": {"target": "Package", "version": "[2.88.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}}