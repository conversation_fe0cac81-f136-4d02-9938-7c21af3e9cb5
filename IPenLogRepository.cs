using System.Collections.Generic;
using System.Threading.Tasks;

namespace TestVoicePen
{
    /// <summary>
    /// 点阵笔日志数据访问接口
    /// 支持从不同数据源（SQL Server、MongoDB）获取数据
    /// </summary>
    public interface IPenLogRepository
    {
        /// <summary>
        /// 获取所有页面列表（去重并排序）
        /// </summary>
        /// <returns>页面ID列表</returns>
        Task<IEnumerable<WorkbookPage>> GetPagesAsync();

        /// <summary>
        /// 根据页面ID获取该页面的所有点位数据
        /// </summary>
        /// <param name="pageId">页面ID</param>
        /// <returns>点位数据列表</returns>
        Task<IEnumerable<PenLogPoint>> GetPointsByPageAsync(int pageId);

        /// <summary>
        /// 清空所有点阵笔日志数据
        /// </summary>
        /// <returns></returns>
        Task ClearAllLogsAsync();
    }

    /// <summary>
    /// 扁平化的点位数据模型
    /// 用于统一SQL和MongoDB的数据结构差异
    /// </summary>
    public class PenLogPoint
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public long Oid { get; set; }

        /// <summary>
        /// X坐标
        /// </summary>
        public int X { get; set; }

        /// <summary>
        /// Y坐标
        /// </summary>
        public int Y { get; set; }

        /// <summary>
        /// 点阵笔Mac地址
        /// </summary>
        public string PMac { get; set; }

        /// <summary>
        /// 页面编号
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// 点位类型：1.开始 2.结束
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 用户ID（MongoDB专用）
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 压力值
        /// </summary>
        public int Pressure { get; set; }

        /// <summary>
        /// 书本编号
        /// </summary>
        public int BookNo { get; set; }
    }
}
