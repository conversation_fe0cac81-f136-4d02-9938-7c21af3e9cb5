using Azure;
using Dapper;
using Microsoft.Data.SqlClient;
using MongoDB.Driver;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace TestVoicePen;

internal class Program
{
    static async Task Main(string[] args)
    {
        // 初始化配置
        var config = new AppConfig();

        // 解析命令行参数
        // 例如: TestVoicePen.exe --datasource=mongodb --mac=AA:BB:CC:DD:EE:FF
        foreach (var arg in args)
        {
            if (arg.StartsWith("--datasource="))
            {
                var dataSource = arg.Split('=')[1].ToLower();
                config.CurrentDataSource = dataSource switch
                {
                    "sqlserver" or "sql" => AppConfig.DataSourceType.SqlServer,
                    "mongodb" or "mongo" => AppConfig.DataSourceType.MongoDB,
                    _ => config.CurrentDataSource
                };
            }
            else if (arg.StartsWith("--mac="))
            {
                var macAddress = arg.Split('=')[1];
                config.MongoDB.MacAddress = macAddress;
                // 注意：如果需要，也可以为SQL Server配置添加Mac地址字段
            }
        }

        Console.WriteLine($"使用数据源: {config.GetCurrentDataSourceName()}");
        if (!string.IsNullOrEmpty(config.MongoDB.MacAddress))
        {
            Console.WriteLine($"过滤Mac地址: {config.MongoDB.MacAddress}");
        }
        Console.WriteLine($"查询日期: {DateTime.Today:yyyy-MM-dd} (当天)");

        // 创建数据访问层实例
        IPenLogRepository penLogRepository = config.CurrentDataSource switch
        {
            AppConfig.DataSourceType.SqlServer => new SqlPenLogRepository(
                config.SqlServer.PenLogConnectionString,
                config.MongoDB.MacAddress), // 复用MongoDB的Mac配置
            AppConfig.DataSourceType.MongoDB => new MongoPenLogRepository(
                config.MongoDB.ConnectionString,
                config.MongoDB.DatabaseName,
                config.MongoDB.CollectionName,
                config.MongoDB.MacAddress),
            _ => throw new NotSupportedException($"不支持的数据源类型: {config.CurrentDataSource}")
        };

        // 创建WorkbookPage数据访问连接（目前仍使用SQL）
        var workbookConnectionString = config.CurrentDataSource switch
        {
            AppConfig.DataSourceType.SqlServer => config.SqlServer.WorkbookConnectionString,
            AppConfig.DataSourceType.MongoDB => config.MongoDB.WorkbookConnectionString,
            _ => throw new NotSupportedException($"不支持的数据源类型: {config.CurrentDataSource}")
        };
        await using var workbookConnection = new SqlConnection(workbookConnectionString);

        // 获取页面列表
        Console.WriteLine("正在获取页面列表...");
        var pagelist = await penLogRepository.GetPagesAsync();
        var basedir = Path.Combine(AppContext.BaseDirectory, "OriginImages");
        if (!Directory.Exists(basedir))
        {
            Directory.CreateDirectory(basedir);
        }

        Console.WriteLine($"找到 {pagelist.Count()} 个页面，开始处理...");

        foreach (var page in pagelist)
        {
            Console.WriteLine($"正在处理页面: {page.PageId}");

            // 获取WorkbookPage信息
            var wsql = @"SELECT TOP (1)
                               [PageId],
                               [ImgUrl]
                        FROM [dbo].[WorkbookPage]
                        WHERE [PageId] = @pageid;";

            var workpage = await workbookConnection.QueryFirstOrDefaultAsync<WorkbookPage>(wsql, new
            {
                pageid = page.PageId
            });
            if (workpage == null)
            {
                Console.WriteLine($"页面 {page.PageId} 没有找到对应的WorkbookPage，跳过");
                continue;
            }

            // 下载并保存原始图片
            using var httpClient = new HttpClient();
            var bites = await httpClient.GetByteArrayAsync(workpage.ImgUrl);
            var name = Path.Combine(basedir, $"{workpage.PageId}.jpg");
            await using var stream = new FileStream(name, FileMode.Create, FileAccess.ReadWrite);
            await stream.WriteAsync(bites);

            // 创建画布
            using var bgimg = SKImage.FromEncodedData(name);
            var imginfo = new SKImageInfo(bgimg.Width, bgimg.Height);
            using var surface = SKSurface.Create(imginfo);
            using var canvas = surface.Canvas;
            var scale = Convert.ToSingle(600) / Convert.ToSingle(200);
            const int xscale = 100;
            canvas.Clear(SKColors.White);

            // 设置画笔
            using var paint = new SKPaint();
            paint.StrokeWidth = 1F;
            paint.IsAutohinted = true;
            paint.IsAntialias = true;
            paint.IsStroke = true;
            paint.IsDither = true;
            paint.IsEmbeddedBitmapText = true;
            paint.IsLinearText = true;
            paint.HintingLevel = SKPaintHinting.Full;
            paint.Color = SKColors.Black;
            paint.Style = SKPaintStyle.Stroke;
            paint.Typeface = SKTypeface.FromFamilyName("微软雅黑", SKFontStyle.Normal);
            canvas.DrawImage(bgimg, 0, 0);
            canvas.Save();
            canvas.Restore();

            // 从数据源获取点位数据
            var points = await penLogRepository.GetPointsByPageAsync(page.PageId);
            var xpoints = points.ToList();
            Console.WriteLine($"页面 {page.PageId} 获取到 {xpoints.Count} 个点位");
            // 开始和结束点位分组
            var pointgroups = new List<List<PenLogPoint>>();
            // 临时点位分组
            var xylist = new List<PenLogPoint>();
            foreach (var xitem in xpoints)
            {
                xylist.Add(xitem);
                if (xitem.Type != 2)
                {
                    continue;
                }

                pointgroups.Add(xylist);
                xylist = new List<PenLogPoint>();
            }

            Console.WriteLine($"页面 {page.PageId} 分组为 {pointgroups.Count} 个笔画");

            foreach (var item in pointgroups)
            {
                var zpoints =
                    (from zitem in item
                     let zx = Convert.ToSingle(zitem.X) / Convert.ToSingle(scale)
                     let zy = Convert.ToSingle(zitem.Y) / Convert.ToSingle(scale)
                     select new SKPoint(zx, zy)).ToList();
                using var spath = new SKPath();
                var first = zpoints.FirstOrDefault();
                spath.MoveTo(first.X, first.Y);
                zpoints.RemoveAt(0);
                foreach (var xitem in zpoints)
                {
                    var last = spath.LastPoint;
                    if (Math.Abs(last.X - xitem.X) > xscale || Math.Abs(last.Y - xitem.Y) > xscale)
                    {
                        spath.Close();
                    }
                    else
                    {
                        spath.LineTo(xitem.X, xitem.Y);
                    }

                    spath.MoveTo(xitem.X, xitem.Y);
                }

                canvas.Save();
                canvas.Restore();
                canvas.DrawPath(spath, paint);
            }

            using var ximg = surface.Snapshot();
            using var data = ximg.Encode(SKEncodedImageFormat.Jpeg, 100);
            var imgid = $"{page.PageId}.jpg";
            var xdir = Path.Combine(AppContext.BaseDirectory, "FinalPages");
            if (!Directory.Exists(xdir))
            {
                Directory.CreateDirectory(xdir);
            }

            var zdir = Path.Combine(xdir, imgid);
            await using var files = new FileStream(zdir, FileMode.Create, FileAccess.ReadWrite);
            data.SaveTo(files);
        }

        Console.WriteLine("所有页面处理完成!");

        // 清空数据源中的日志数据
        Console.WriteLine("正在清空日志数据...");
        await penLogRepository.ClearAllLogsAsync();
        Console.WriteLine($"数据清理完成, {DateTime.Now:G}");

        Console.WriteLine("按任意键退出...");
        Console.ReadLine();
    }
}