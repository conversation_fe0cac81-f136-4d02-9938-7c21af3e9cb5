using Azure;
using Dapper;
using Microsoft.Data.SqlClient;
using MongoDB.Driver;
using SkiaSharp;

namespace TestVoicePen;

internal class Program
{
    static async Task Main(string[] args)
    {
        //develop
        const string dbcfg = "Server=***************;Database=YouwoEduPlatfrom;User ID=dev;Password=***$qwerASDFzxcv;Trusted_Connection=false;Connect Timeout=720;MultipleActiveResultSets=true;Max Pool Size=512; Min Pool Size=5;encrypt=True;TrustServerCertificate=True;";
        await using var dapper = new SqlConnection(dbcfg);

        //eduwon
        const string xdbcfg = "Server=***************;Database=YouwoEduPlatfrom;User ID=dev;Password=***$qwerASDFzxcv;Trusted_Connection=false;Connect Timeout=720;MultipleActiveResultSets=true;Max Pool Size=512; Min Pool Size=5;encrypt=True;TrustServerCertificate=True;";
        await using var xdapper = new SqlConnection(xdbcfg);

        var client = new MongoClient("mongodb://Lucifer:Uwoo%40123!%40#@**************:27017/?directConnection=true");
        var _database = client.GetDatabase("PenMongo");
        var collection = _database.GetCollection<PenLog>("学校Id");
        var penlogs = await collection.Find(_ => true).ToListAsync();

        var psql = @"SELECT [Page] AS [PageId]
                    FROM [dbo].[ZPenLog]
                    GROUP BY [Page]
                    ORDER BY [Page];";
        var pagelist = await dapper.QueryAsync<WorkbookPage>(psql);
        var basedir = Path.Combine(AppContext.BaseDirectory, "OriginImages");
        if (!Directory.Exists(basedir))
        {
            Directory.CreateDirectory(basedir);
        }
        foreach (var page in pagelist)
        {
            var wsql = @"SELECT TOP (1)
                               [PageId],
                               [ImgUrl]
                        FROM [dbo].[WorkbookPage]
                        WHERE [PageId] = @pageid;";

            var workpage = await xdapper.QueryFirstOrDefaultAsync<WorkbookPage>(wsql, new
            {
                pageid = page.PageId
            });
            if (workpage == null)
            {
                continue;
            }

            using var client = new HttpClient();
            var bites = await client.GetByteArrayAsync(workpage.ImgUrl);
            var name = Path.Combine(basedir, $"{workpage.PageId}.jpg");
            await using var stream = new FileStream(name, FileMode.Create,
                FileAccess.ReadWrite);
            await stream.WriteAsync(bites, 0, bites.Length);
            using var bgimg = SKImage.FromEncodedData(name);
            var imginfo = new SKImageInfo(bgimg.Width, bgimg.Height);
            using var surface = SKSurface.Create(imginfo);
            using var canvas = surface.Canvas;
            var scale = Convert.ToSingle(600) / Convert.ToSingle(200);
            const int xscale = 100;
            canvas.Clear(SKColors.White);
            using var paint = new SKPaint();
            paint.StrokeWidth = 1F;
            paint.IsAutohinted = true;
            paint.IsAntialias = true;
            paint.IsStroke = true;
            paint.IsDither = true;
            paint.IsEmbeddedBitmapText = true;
            paint.IsLinearText = true;
            paint.HintingLevel = SKPaintHinting.Full;
            paint.Color = SKColors.Black;
            paint.Style = SKPaintStyle.Stroke;
            paint.Typeface = SKTypeface.FromFamilyName("微软雅黑", SKFontStyle.Normal);
            canvas.DrawImage(bgimg, 0, 0);
            canvas.Save();
            canvas.Restore();
            const string sql = @$"SELECT [Oid],
                                           [X],
                                           [Y],
                                           [PMac],
                                           [Page],
                                           [Type]
                                    FROM [TestDotPen].[dbo].[ZPenLog]
                                    WHERE [Page] = @page;";
            var points = await dapper.QueryAsync<PenLog>(sql, new {page = page.PageId });
            //所有点位
            var xpoints = points.ToList();
            //开始和结束点位分组
            var pointgroups = new List<List<PenLog>>();
            //临时点位分组
            var xylist = new List<PenLog>();
            foreach (var xitem in xpoints)
            {
                xylist.Add(xitem);
                if (xitem.Type != 2)
                {
                    continue;
                }

                pointgroups.Add(xylist);
                xylist = new List<PenLog>();
            }

            foreach (var item in pointgroups)
            {
                var zpoints =
                    (from zitem in item
                     let zx = Convert.ToSingle(zitem.X) / Convert.ToSingle(scale)
                     let zy = Convert.ToSingle(zitem.Y) / Convert.ToSingle(scale)
                     select new SKPoint(zx, zy)).ToList();
                using var spath = new SKPath();
                var first = zpoints.FirstOrDefault();
                spath.MoveTo(first.X, first.Y);
                zpoints.RemoveAt(0);
                foreach (var xitem in zpoints)
                {
                    var last = spath.LastPoint;
                    if (Math.Abs(last.X - xitem.X) > xscale || Math.Abs(last.Y - xitem.Y) > xscale)
                    {
                        spath.Close();
                    }
                    else
                    {
                        spath.LineTo(xitem.X, xitem.Y);
                    }

                    spath.MoveTo(xitem.X, xitem.Y);
                }

                canvas.Save();
                canvas.Restore();
                canvas.DrawPath(spath, paint);
            }

            using var ximg = surface.Snapshot();
            using var data = ximg.Encode(SKEncodedImageFormat.Jpeg, 100);
            var imgid = $"{page.PageId}.jpg";
            var xdir = Path.Combine(AppContext.BaseDirectory, "FinalPages");
            if (!Directory.Exists(xdir))
            {
                Directory.CreateDirectory(xdir);
            }

            var zdir = Path.Combine(xdir, imgid);
            await using var files = new FileStream(zdir, FileMode.Create, FileAccess.ReadWrite);
            data.SaveTo(files);
        }

        Console.WriteLine("finished...");
        const string dsql = @"TRUNCATE TABLE [dbo].[ZPenLog];";
        await dapper.ExecuteAsync(dsql);
        Console.WriteLine($"clear , {DateTime.Now:G}");
        Console.ReadLine();
    }
}