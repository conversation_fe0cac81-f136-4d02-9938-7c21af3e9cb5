# TestVoicePen 配置示例

## 使用方法

### 1. 使用MongoDB数据源（默认）
# 查询所有Mac地址的当天数据
TestVoicePen.exe

# 查询指定Mac地址的当天数据
TestVoicePen.exe --mac=AA:BB:CC:DD:EE:FF

### 2. 使用SQL Server数据源
# 查询所有Mac地址的当天数据
TestVoicePen.exe --datasource=sqlserver

# 查询指定Mac地址的当天数据
TestVoicePen.exe --datasource=sqlserver --mac=00E04C0022DD

## 配置修改

如需修改数据库连接信息，请编辑 AppConfig.cs 文件：

### MongoDB配置
- ConnectionString: MongoDB连接字符串
- DatabaseName: 数据库名称 (默认: PenMongo)
- CollectionName: 集合名称 (默认: PenLog_2024_1742008546532302848)
- MacAddress: 默认Mac地址 (可通过命令行覆盖)

### SQL Server配置
- PenLogConnectionString: ZPenLog表连接字符串
- WorkbookConnectionString: WorkbookPage表连接字符串

## 常用Mac地址示例
# 请替换为实际的Mac地址
00E04C0022DD

## 数据过滤说明
- 程序只处理当天的数据
- 如果指定Mac地址，只处理该Mac地址的数据
- 数据清理也只清理符合条件的数据，不会影响其他数据
