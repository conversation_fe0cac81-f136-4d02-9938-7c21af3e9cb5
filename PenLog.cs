//  -- Function：PenLog.cs
//  --- Project：TestCanvas
//  ---- Remark：
//  ---- Author：Lucifer
//  ------ Date：2023/04/09 23:46:02

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System.Text.Json.Serialization;

namespace TestVoicePen;

public class PenLog: MongoBaseModel
{
    /// <summary>
    /// 点阵笔采集到的页码
    /// </summary>
    [BsonElement(nameof(Page))]
    [BsonRepresentation(BsonType.Int32)]
    [JsonPropertyName(nameof(Page))]
    [JsonInclude]
    public int Page { get; set; }

    /// <summary>
    /// 对应业务页码id, 卷码纸为实际的卷码, 其余情况和Page字段值一致
    /// </summary>
    /// <seealso cref="Page"/>
    public int PageId { get; set; }

    /// <summary>
    /// 点阵笔Mac地址
    /// </summary>
    public string Mac { get; set; }

    /// <summary>
    /// 用户id
    /// </summary>
    public string UserId { get; set; }

    /// <summary>
    /// 点位列表
    /// </summary>
    public List<DotBase> Dots { get; set; } = new();
}