using System;

namespace TestVoicePen
{
    /// <summary>
    /// 应用程序配置类
    /// 管理数据源选择和连接配置
    /// </summary>
    public class AppConfig
    {
        /// <summary>
        /// 数据源类型枚举
        /// </summary>
        public enum DataSourceType
        {
            /// <summary>
            /// SQL Server数据库
            /// </summary>
            SqlServer,
            
            /// <summary>
            /// MongoDB数据库
            /// </summary>
            MongoDB
        }

        /// <summary>
        /// 当前使用的数据源类型
        /// </summary>
        public DataSourceType CurrentDataSource { get; set; } = DataSourceType.MongoDB;

        /// <summary>
        /// SQL Server配置
        /// </summary>
        public SqlServerConfig SqlServer { get; set; } = new();

        /// <summary>
        /// MongoDB配置
        /// </summary>
        public MongoDbConfig MongoDB { get; set; } = new();

        /// <summary>
        /// 获取当前数据源的显示名称
        /// </summary>
        /// <returns></returns>
        public string GetCurrentDataSourceName()
        {
            return CurrentDataSource switch
            {
                DataSourceType.SqlServer => "SQL Server",
                DataSourceType.MongoDB => "MongoDB",
                _ => "Unknown"
            };
        }
    }

    /// <summary>
    /// SQL Server连接配置
    /// </summary>
    public class SqlServerConfig
    {
        /// <summary>
        /// 主数据库连接字符串（用于读取ZPenLog）
        /// </summary>
        public string PenLogConnectionString { get; set; } = 
            "Server=***************;Database=YouwoEduPlatfrom;User ID=dev;Password=***$qwerASDFzxcv;Trusted_Connection=false;Connect Timeout=720;MultipleActiveResultSets=true;Max Pool Size=512; Min Pool Size=5;encrypt=True;TrustServerCertificate=True;";

        /// <summary>
        /// 辅助数据库连接字符串（用于读取WorkbookPage）
        /// </summary>
        public string WorkbookConnectionString { get; set; } = 
            "Server=***************;Database=YouwoEduPlatfrom;User ID=dev;Password=***$qwerASDFzxcv;Trusted_Connection=false;Connect Timeout=720;MultipleActiveResultSets=true;Max Pool Size=512; Min Pool Size=5;encrypt=True;TrustServerCertificate=True;";
    }

    /// <summary>
    /// MongoDB连接配置
    /// </summary>
    public class MongoDbConfig
    {
        /// <summary>
        /// MongoDB连接字符串
        /// </summary>
        public string ConnectionString { get; set; } =
            "mongodb://Lucifer:Uwoo%40123!%40#@**************:27017/?directConnection=true";

        /// <summary>
        /// 数据库名称
        /// </summary>
        public string DatabaseName { get; set; } = "PenMongo";

        /// <summary>
        /// 集合名称
        /// </summary>
        public string CollectionName { get; set; } = "1742008546532302848";

        /// <summary>
        /// 要查询的Mac地址，如果为空则查询所有Mac地址的数据
        /// </summary>
        public string MacAddress { get; set; } = "00E04C0022DD";

        /// <summary>
        /// WorkbookPage的SQL连接字符串（MongoDB模式下仍需要从SQL读取图片信息）
        /// </summary>
        public string WorkbookConnectionString { get; set; } =
            "Server=***************;Database=YouwoEduPlatfrom;User ID=dev;Password=***$qwerASDFzxcv;Trusted_Connection=false;Connect Timeout=720;MultipleActiveResultSets=true;Max Pool Size=512; Min Pool Size=5;encrypt=True;TrustServerCertificate=True;";
    }
}
