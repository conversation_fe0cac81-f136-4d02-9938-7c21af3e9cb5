# TestVoicePen - 点阵笔数据处理工具

## 概述

这个工具用于处理点阵笔日志数据，支持从SQL Server和MongoDB两种数据源读取数据，并生成带有笔迹的图片。

## 主要功能

1. **多数据源支持**：支持从SQL Server的`ZPenLog`表或MongoDB的`PenLog`集合读取数据
2. **数据转换**：将MongoDB的嵌套文档结构转换为扁平化的点位数据
3. **智能过滤**：支持按Mac地址和当天日期过滤数据
4. **图片生成**：基于点位数据在原始图片上绘制笔迹
5. **数据清理**：处理完成后清空源数据（仅清理符合过滤条件的数据）

## 数据源配置

### 使用MongoDB（默认）

程序默认使用MongoDB作为数据源。MongoDB配置信息在`AppConfig.cs`中：

```csharp
public class MongoDbConfig
{
    public string ConnectionString { get; set; } = 
        "mongodb://Lucifer:Uwoo%40123!%40#@122.112.252.60:27017/?directConnection=true";
    public string DatabaseName { get; set; } = "PenMongo";
    public string CollectionName { get; set; } = "学校Id";
}
```

### 使用SQL Server

通过命令行参数切换到SQL Server：

```bash
TestVoicePen.exe --datasource=sqlserver
```

或者

```bash
TestVoicePen.exe --datasource=sql
```

## 数据结构对比

### SQL Server (ZPenLog表)
```sql
CREATE TABLE ZPenLog (
    Oid BIGINT,
    X INT,
    Y INT,
    PMac NVARCHAR(50),
    Page INT,
    Type INT
)
```

### MongoDB (PenLog集合)
```json
{
    "_id": ObjectId,
    "Mid": NumberLong,
    "Page": NumberInt,
    "Mac": String,
    "UserId": String,
    "Dots": [
        {
            "Mid": NumberLong,
            "X": NumberInt,
            "Y": NumberInt,
            "Type": NumberInt,
            "BookNo": NumberInt,
            "Pressure": NumberInt
        }
    ],
    "AddTime": ISODate
}
```

## 架构设计

### 核心组件

1. **IPenLogRepository**: 数据访问接口，抽象不同数据源的访问逻辑
2. **SqlPenLogRepository**: SQL Server数据访问实现
3. **MongoPenLogRepository**: MongoDB数据访问实现
4. **AppConfig**: 配置管理类
5. **PenLogPoint**: 统一的点位数据模型

### 设计优势

- **可扩展性**: 通过接口抽象，易于添加新的数据源支持
- **数据一致性**: 统一的数据模型处理不同数据源的结构差异
- **配置化**: 支持运行时切换数据源
- **向后兼容**: 保持原有SQL查询逻辑不变

## 使用方法

### 基本使用

```bash
# 使用默认的MongoDB数据源，查询所有Mac地址的当天数据
TestVoicePen.exe

# 使用SQL Server数据源，查询所有Mac地址的当天数据
TestVoicePen.exe --datasource=sqlserver

# 使用MongoDB数据源，查询指定Mac地址的当天数据
TestVoicePen.exe --datasource=mongodb --mac=AA:BB:CC:DD:EE:FF

# 使用SQL Server数据源，查询指定Mac地址的当天数据
TestVoicePen.exe --datasource=sqlserver --mac=AA:BB:CC:DD:EE:FF
```

### 命令行参数

- `--datasource=<type>`: 指定数据源类型
  - `mongodb` 或 `mongo`: 使用MongoDB数据源
  - `sqlserver` 或 `sql`: 使用SQL Server数据源
- `--mac=<address>`: 指定要查询的Mac地址（可选）
  - 如果不指定，则查询所有Mac地址的数据
  - 格式示例：`AA:BB:CC:DD:EE:FF`

### 输出文件

- `OriginImages/`: 存储下载的原始图片
- `FinalPages/`: 存储生成的带笔迹图片

## 注意事项

1. **数据清理**: 程序会在处理完成后清空源数据，但只清理符合过滤条件的数据（当天+指定Mac）
2. **日期过滤**: 程序只处理当天的数据，基于`AddTime`字段（MongoDB）或`CreateTime`字段（SQL Server）
3. **Mac地址过滤**: 如果指定了Mac地址，只处理该Mac地址的数据
4. **网络连接**: 需要能够访问图片URL和数据库服务器
5. **权限要求**: 需要对数据库有读写权限
6. **MongoDB集合名**: 当前使用"1742008546532302848"作为集合名，可在配置中修改

## 故障排除

### 常见问题

1. **连接失败**: 检查数据库连接字符串和网络连接
2. **权限错误**: 确保数据库用户有足够的权限
3. **图片下载失败**: 检查网络连接和图片URL的有效性
4. **MongoDB查询为空**: 检查集合名称和数据结构是否正确

### 日志输出

程序会输出详细的处理日志，包括：
- 当前使用的数据源
- 找到的页面数量
- 每个页面的点位数量
- 笔画分组信息
- 处理进度

## 开发说明

### 添加新数据源

1. 实现`IPenLogRepository`接口
2. 在`AppConfig`中添加相应的配置类
3. 在`Program.cs`中添加数据源创建逻辑

### 修改数据结构

如需修改点位数据结构，请同时更新：
- `PenLogPoint`类
- 相关的Repository实现
- 数据转换逻辑
